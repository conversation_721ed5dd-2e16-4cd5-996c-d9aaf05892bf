// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Config/NeoPakConfigAssetBase.h"
#include "Utils/NeoAssetDependencyResolver.h"
#include "Config/NeoPakToolsSettings.h"
#include "HAL/CriticalSection.h"
#include <atomic>
#include "NeoPakManager.generated.h"

// PAK加载状态
UENUM(BlueprintType)
enum class EPakLoadStatus : uint8
{
    NotLoaded       UMETA(DisplayName = "Not Loaded"),
    Loading         UMETA(DisplayName = "Loading"),
    Loaded          UMETA(DisplayName = "Loaded"),
    Failed          UMETA(DisplayName = "Failed")
};

// 批量操作结果结构
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoBatchOperationResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    int32 TotalCount = 0;

    UPROPERTY(BlueprintReadOnly)
    int32 SuccessCount = 0;

    UPROPERTY(BlueprintReadOnly)
    int32 FailureCount = 0;

    UPROPERTY(BlueprintReadOnly)
    TArray<FString> FailedItems;

    UPROPERTY(BlueprintReadOnly)
    FString Summary;

    FNeoBatchOperationResult()
    {
        TotalCount = 0;
        SuccessCount = 0;
        FailureCount = 0;
        Summary = TEXT("");
    }
};

// 进度回调委托
DECLARE_MULTICAST_DELEGATE_ThreeParams(FNeoProgressDelegate, int32, int32, const FString&);

// 操作取消委托
DECLARE_MULTICAST_DELEGATE(FNeoOperationCancelledDelegate);

// PAK条目信息
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoPakEntry
{
    GENERATED_BODY()

    // PAK文件路径
    UPROPERTY(BlueprintReadOnly)
    FString PakFilePath;

    // 加载状态
    UPROPERTY(BlueprintReadOnly)
    EPakLoadStatus LoadStatus = EPakLoadStatus::NotLoaded;

    // 包含的资产列表
    UPROPERTY(BlueprintReadOnly)
    TArray<FString> ContainedAssets;

    // 加载时间
    UPROPERTY(BlueprintReadOnly)
    FDateTime LoadTime;
};

UCLASS(BlueprintType)
class NEOPAKTOOLS_API UNeoPakManager : public UObject
{
    GENERATED_BODY()

public:
    // 打包功能（通过DataAsset配置）
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool PackageFromConfig(UNeoPakConfigAssetBase* ConfigAsset);

    // 带依赖检查的打包功能
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool PackageFromConfigWithDependencyCheck(UNeoPakConfigAssetBase* ConfigAsset, bool bAutoFixDependencies = false);

    // 运行时加载
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool LoadPakFile(const FString& PakFilePath);

    // 卸载PAK
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool UnloadPakFile(const FString& PakFilePath);

    // 依赖检查
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FString> GetMissingDependencies(const FString& PakFilePath);

    // Runtime资产加载辅助功能
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Runtime")
    bool IsPakFileLoaded(const FString& PakFilePath);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Runtime")
    TArray<FString> GetLoadedPakFiles();

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Runtime")
    bool LoadAssetFromPak(const FString& AssetPath, UObject*& OutAsset);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Runtime")
    bool VerifyPakIntegrity(const FString& PakFilePath);

    // 获取已加载的PAK列表
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FNeoPakEntry> GetLoadedPaks() const;

    // 获取PAK加载状态
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    EPakLoadStatus GetPakLoadStatus(const FString& PakFilePath) const;

    // 获取单例实例
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static UNeoPakManager* GetInstance();

    // 扫描指定目录中的PAK文件
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    void ScanForPakFiles(const FString& Directory = TEXT(""));

    // 获取所有发现的PAK文件（包括已加载和未加载的）
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FNeoPakEntry> GetAllDiscoveredPaks() const;

    // 获取PAK文件中的文件列表
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FString> GetPakFileContents(const FString& PakFilePath);

    // 获取PAK文件信息（文件数量、大小等）
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool GetPakFileInfo(const FString& PakFilePath, int32& OutFileCount, int64& OutTotalSize);

    // 从PAK文件加载配置资产
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    UNeoPakConfigAssetBase* LoadConfigAssetFromPak(const FString& PakFilePath);

    // 批量操作功能
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool BatchPackageConfigs(const TArray<UNeoPakConfigAssetBase*>& Configs, bool bShowProgress = true);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FString> BatchValidateConfigs(const TArray<UNeoPakConfigAssetBase*>& Configs);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool CleanOutputDirectory(bool bConfirmDelete = true);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<UNeoPakConfigAssetBase*> GetAllConfigAssets();

    // 高级打包功能
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool CreatePakFileWithAdvancedOptions(const TArray<FString>& AssetPaths, const FString& OutputPath, const FNeoPakCompressionSettings& CompressionSettings, const FNeoPakEncryptionSettings& EncryptionSettings);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool CreateIncrementalPakFile(const TArray<FString>& AssetPaths, const FString& OutputPath, const FString& BasePakPath);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool VerifyPakFileIntegrity(const FString& PakFilePath);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    FNeoDependencyCheckResult CheckDependencies(UNeoPakConfigAssetBase* ConfigAsset);

    // 进度跟踪和取消功能
    FNeoProgressDelegate OnProgressUpdated;
    FNeoOperationCancelledDelegate OnOperationCancelled;

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    void CancelCurrentOperation();

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool IsOperationInProgress() const;

private:
    // 已加载的PAK文件映射
    UPROPERTY()
    TMap<FString, FNeoPakEntry> LoadedPaks;

    // 发现的所有PAK文件（包括未加载的）
    UPROPERTY()
    TMap<FString, FNeoPakEntry> DiscoveredPaks;

    // 依赖解析器
    TSharedPtr<FNeoAssetDependencyResolver> DependencyResolver;

    // 进度跟踪和取消控制
    mutable FCriticalSection OperationLock;
    std::atomic<bool> bOperationInProgress;
    std::atomic<bool> bCancelRequested;
    int32 CurrentOperationStep;
    int32 TotalOperationSteps;
    FString CurrentOperationName;

    // 单例实例
    static UNeoPakManager* Instance;

    // 执行实际的打包操作
    bool ExecutePackaging(UNeoPakConfigAssetBase* ConfigAsset);

    // 创建PAK文件
    bool CreatePakFile(const TArray<FString>& AssetPaths, const FString& OutputPath);

    // 验证PAK文件
    bool ValidatePakFile(const FString& PakFilePath);

    // 解析PAK文件内容
    bool ParsePakFileContents(const FString& PakFilePath, TArray<FString>& OutFileList);

    // 更新PAK条目的文件列表
    void UpdatePakEntryContents(const FString& PakFilePath);

    // 解析依赖并返回完整的资产列表（用于打包）
    TArray<FString> ResolveDependenciesForPackaging(const TArray<FString>& MainAssetPaths);

    // 判断是否应该包含某个依赖资产
    bool ShouldIncludeDependency(const FString& DependencyPath);

    // 批量操作辅助方法
    void FindAllConfigAssets(TArray<UNeoPakConfigAssetBase*>& OutConfigs);
    bool DeleteFilesInDirectory(const FString& DirectoryPath, const TArray<FString>& FileExtensions);

    // 依赖修复辅助方法
    FString ConvertFilePathToAssetPath(const FString& FilePath);
    TArray<FString> FindAvailableAssets(const TArray<FString>& MissingDependencies);
    bool CanAutoFixDependency(const FString& DependencyPath);
    bool AutoFixDependencies(UNeoPakConfigAssetBase* ConfigAsset, const FNeoDependencyCheckResult& DependencyResult);

    // 具体的依赖修复方法
    FString ExtractDependencyPathFromIssue(const FString& Issue);
    bool FixMissingDependencies(UNeoPakConfigAssetBase* ConfigAsset, const TArray<FString>& MissingDependencies);
    bool FixConflictingDependencies(UNeoPakConfigAssetBase* ConfigAsset, const TArray<FString>& ConflictingDependencies);
    bool FixCircularDependencies(UNeoPakConfigAssetBase* ConfigAsset, const TArray<FString>& CircularDependencies);
    bool AddAssetToConfig(UNeoPakConfigAssetBase* ConfigAsset, const FString& AssetPath);

    // 高级打包辅助方法
    FString BuildAdvancedUnrealPakCommand(const FString& PakFilePath, const FString& ResponseFilePath, const FNeoPakCompressionSettings& CompressionSettings, const FNeoPakEncryptionSettings& EncryptionSettings);
    bool CreateAdvancedResponseFile(const TArray<FString>& AssetPaths, const FString& ResponseFilePath, const FNeoPakCompressionSettings& CompressionSettings);
    bool GeneratePakSignature(const FString& PakFilePath);
    bool CheckIncrementalChanges(const TArray<FString>& AssetPaths, const FString& BasePakPath, TArray<FString>& OutChangedAssets);

    // 工具路径辅助方法
    FString GetUnrealPakPath() const;
    TArray<FString> ConvertAssetPathsToFilePaths(const TArray<FString>& AssetPaths) const;
    FString ConvertAssetPathAlternativeInternal(const FString& AssetPath) const;
    int32 GetNextPakOrder() const;

    // 进度跟踪辅助方法
    void StartOperation(const FString& OperationName, int32 TotalSteps);
    void UpdateProgress(int32 CurrentStep, const FString& CurrentStepName = TEXT(""));
    void FinishOperation();
    bool ShouldCancelOperation() const;
};
