// Copyright Epic Games, Inc. All Rights Reserved.

#include "PakManager/NeoPakManager.h"
#include "NeoPakTools.h"
#include "Config/NeoPakToolsSettings.h"
#include "Utils/NeoDataAssetTypeRegistry.h"
#include "Utils/NeoPakFileCreator.h"
#include "HAL/PlatformFilemanager.h"
#include "HAL/FileManager.h"
#include "Misc/Paths.h"
#include "Engine/Engine.h"
#include "IPlatformFilePak.h"
#include "HAL/PlatformProcess.h"
#include "GenericPlatform/GenericPlatformFile.h"
#include "HAL/PlatformFileManager.h"
#include "AssetRegistry/AssetRegistryModule.h"

// 静态实例
UNeoPakManager* UNeoPakManager::Instance = nullptr;

UNeoPakManager* UNeoPakManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UNeoPakManager>();
        Instance->AddToRoot(); // 防止被垃圾回收
        Instance->DependencyResolver = MakeShared<FNeoAssetDependencyResolver>();

        // 初始化进度跟踪变量
        Instance->bOperationInProgress.store(false);
        Instance->bCancelRequested.store(false);
        Instance->CurrentOperationStep = 0;
        Instance->TotalOperationSteps = 0;
        Instance->CurrentOperationName = TEXT("");
    }
    return Instance;
}

bool UNeoPakManager::PackageFromConfig(UNeoPakConfigAssetBase* ConfigAsset)
{
    if (!ConfigAsset)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("ConfigAsset is null"));
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Starting packaging for config: %s"), *ConfigAsset->ConfigName);

#if WITH_EDITOR
    // 验证配置
    if (!ConfigAsset->ValidateConfiguration())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Configuration validation failed for: %s"), *ConfigAsset->ConfigName);
        return false;
    }
#endif

    // 执行打包
    return ExecutePackaging(ConfigAsset);
}

bool UNeoPakManager::PackageFromConfigWithDependencyCheck(UNeoPakConfigAssetBase* ConfigAsset, bool bAutoFixDependencies)
{
    if (!ConfigAsset)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("ConfigAsset is null"));
        return false;
    }

    // 检查依赖关系
    if (DependencyResolver)
    {
        FNeoDependencyCheckResult DependencyResult = DependencyResolver->CheckDataAssetDirectoryDependencies(ConfigAsset, true);
        
        if (!DependencyResult.bCheckPassed)
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Dependency check failed for config: %s"), *ConfigAsset->ConfigName);
            
            if (!bAutoFixDependencies)
            {
                return false;
            }

            // 实现自动修复依赖的逻辑
            UE_LOG(LogNeoPakTools, Log, TEXT("Attempting to auto-fix dependencies for config: %s"), *ConfigAsset->ConfigName);

            bool bFixSuccessful = AutoFixDependencies(ConfigAsset, DependencyResult);

            if (!bFixSuccessful)
            {
                UE_LOG(LogNeoPakTools, Error, TEXT("Failed to auto-fix dependencies for config: %s"), *ConfigAsset->ConfigName);
                return false;
            }

            UE_LOG(LogNeoPakTools, Log, TEXT("Successfully auto-fixed dependencies for config: %s"), *ConfigAsset->ConfigName);
        }
    }

    return PackageFromConfig(ConfigAsset);
}







TArray<FNeoPakEntry> UNeoPakManager::GetLoadedPaks() const
{
    TArray<FNeoPakEntry> Result;

    // 如果有发现的PAK文件，返回所有发现的PAK文件
    if (DiscoveredPaks.Num() > 0)
    {
        DiscoveredPaks.GenerateValueArray(Result);
    }
    else
    {
        // 否则返回手动加载的PAK文件
        LoadedPaks.GenerateValueArray(Result);
    }

    return Result;
}



bool UNeoPakManager::ExecutePackaging(UNeoPakConfigAssetBase* ConfigAsset)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Starting packaging for config: %s (Type: %s)"),
           *ConfigAsset->ConfigName, *ConfigAsset->GetClass()->GetName());

    // 获取要打包的资产列表
    TArray<FSoftObjectPath> AssetsToPackage = ConfigAsset->GetAssetsToPackage();

    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d assets to package"), AssetsToPackage.Num());

    if (AssetsToPackage.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No assets to package for config: %s. Please check that all required assets are set in the configuration."), *ConfigAsset->ConfigName);
        return false;
    }

    // 转换为字符串数组
    TArray<FString> AssetPaths;
    for (const FSoftObjectPath& AssetPath : AssetsToPackage)
    {
        AssetPaths.Add(AssetPath.ToString());
    }

    // 添加配置资产本身到打包列表
    FString ConfigAssetPath = ConfigAsset->GetPackage()->GetName();
    AssetPaths.Add(ConfigAssetPath);
    UE_LOG(LogNeoPakTools, Log, TEXT("Added config asset to package: %s"), *ConfigAssetPath);

    // 解析依赖并添加到资产列表（从配置资产开始解析）
    TArray<FString> AllAssetPaths = ResolveDependenciesForPackaging(AssetPaths);

    UE_LOG(LogNeoPakTools, Log, TEXT("Total assets to package (including dependencies): %d"), AllAssetPaths.Num());

    // 获取输出路径
    FString OutputPath = ConfigAsset->GetFullOutputPath();

    // 创建PAK文件
    bool bPakCreated = CreatePakFile(AllAssetPaths, OutputPath);

    if (bPakCreated)
    {
        // 注册DataAsset类型信息到PAK文件
        FNeoDataAssetTypeRegistry::RegisterDataAssetTypeInPak(OutputPath, ConfigAsset);
    }

    return bPakCreated;
}

bool UNeoPakManager::CreatePakFile(const TArray<FString>& AssetPaths, const FString& OutputPath)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Creating PAK file: %s"), *OutputPath);
    UE_LOG(LogNeoPakTools, Log, TEXT("Assets to package: %d"), AssetPaths.Num());

    for (const FString& AssetPath : AssetPaths)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("  - %s"), *AssetPath);
    }

    // 检查是否应该使用高级选项
    const UNeoPakToolsSettings* Settings = GetDefault<UNeoPakToolsSettings>();
    if (Settings && (Settings->DefaultCompressionSettings.bEnableCompression || Settings->DefaultEncryptionSettings.bEnableEncryption))
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Using advanced packaging options from settings"));
        return CreatePakFileWithAdvancedOptions(AssetPaths, OutputPath, Settings->DefaultCompressionSettings, Settings->DefaultEncryptionSettings);
    }
    else
    {
        // Use the real PAK file creator
        bool bSuccess = FNeoPakFileCreator::CreatePakFromAssets(AssetPaths, OutputPath);

        if (bSuccess)
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("PAK file creation completed: %s"), *OutputPath);
        }
        else
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Failed to create PAK file: %s"), *OutputPath);
        }

        return bSuccess;
    }
}

bool UNeoPakManager::ValidatePakFile(const FString& PakFilePath)
{
    // 检查文件是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("PAK file does not exist: %s"), *PakFilePath);
        return false;
    }

    // TODO: 实现PAK文件完整性验证
    UE_LOG(LogNeoPakTools, Log, TEXT("PAK file validation passed: %s"), *PakFilePath);
    return true;
}

void UNeoPakManager::ScanForPakFiles(const FString& Directory)
{
    FString ScanDirectory = Directory;

    // 如果没有指定目录，使用插件设置的默认输出目录
    if (ScanDirectory.IsEmpty())
    {
        const UNeoPakToolsSettings* Settings = GetDefault<UNeoPakToolsSettings>();
        if (Settings)
        {
            // 直接使用默认输出目录路径
            ScanDirectory = Settings->DefaultOutputDirectory.Path;

            // 如果是相对路径，转换为绝对路径
            if (FPaths::IsRelative(ScanDirectory))
            {
                ScanDirectory = FPaths::ProjectDir() / ScanDirectory;
            }

            // 确保路径是绝对路径
            ScanDirectory = FPaths::ConvertRelativePathToFull(ScanDirectory);
        }
        else
        {
            // 默认扫描项目根目录下的Paks文件夹
            ScanDirectory = FPaths::ProjectDir() / TEXT("Paks");
        }
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Scanning for PAK files in directory: %s"), *ScanDirectory);

    // 清空之前发现的PAK文件
    DiscoveredPaks.Empty();

    // 检查目录是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*ScanDirectory))
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("PAK scan directory does not exist: %s"), *ScanDirectory);
        return;
    }

    // 查找所有.pak文件
    TArray<FString> FoundPakFiles;
    IFileManager::Get().FindFilesRecursive(FoundPakFiles, *ScanDirectory, TEXT("*.pak"), true, false);

    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d PAK files"), FoundPakFiles.Num());

    // 为每个发现的PAK文件创建条目
    for (const FString& PakFilePath : FoundPakFiles)
    {
        FNeoPakEntry PakEntry;
        PakEntry.PakFilePath = PakFilePath;

        // 检查是否已经加载
        if (LoadedPaks.Contains(PakFilePath))
        {
            PakEntry.LoadStatus = EPakLoadStatus::Loaded;
            PakEntry.LoadTime = LoadedPaks[PakFilePath].LoadTime;
        }
        else
        {
            PakEntry.LoadStatus = EPakLoadStatus::NotLoaded;
        }

        // 获取文件信息
        FFileStatData StatData = IFileManager::Get().GetStatData(*PakFilePath);
        if (StatData.bIsValid)
        {
            // 可以添加文件大小、修改时间等信息
        }

        DiscoveredPaks.Add(PakFilePath, PakEntry);
        UE_LOG(LogNeoPakTools, Log, TEXT("Discovered PAK file: %s (Status: %s)"),
               *PakFilePath,
               PakEntry.LoadStatus == EPakLoadStatus::Loaded ? TEXT("Loaded") : TEXT("Not Loaded"));
    }
}

TArray<FNeoPakEntry> UNeoPakManager::GetAllDiscoveredPaks() const
{
    TArray<FNeoPakEntry> Result;
    DiscoveredPaks.GenerateValueArray(Result);
    return Result;
}

TArray<FString> UNeoPakManager::GetPakFileContents(const FString& PakFilePath)
{
    TArray<FString> FileList;

    if (PakFilePath.IsEmpty())
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("PAK file path is empty"));
        return FileList;
    }

    // 检查文件是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("PAK file does not exist: %s"), *PakFilePath);
        return FileList;
    }

    // 尝试解析PAK文件内容
    if (ParsePakFileContents(PakFilePath, FileList))
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Successfully read %d files from PAK: %s"), FileList.Num(), *PakFilePath);

        // 更新缓存的PAK条目信息
        if (DiscoveredPaks.Contains(PakFilePath))
        {
            DiscoveredPaks[PakFilePath].ContainedAssets = FileList;
        }
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to read PAK file contents: %s"), *PakFilePath);
    }

    return FileList;
}

bool UNeoPakManager::GetPakFileInfo(const FString& PakFilePath, int32& OutFileCount, int64& OutTotalSize)
{
    OutFileCount = 0;
    OutTotalSize = 0;

    if (PakFilePath.IsEmpty())
    {
        return false;
    }

    // 检查文件是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath))
    {
        return false;
    }

    // 获取PAK文件大小
    FFileStatData StatData = IFileManager::Get().GetStatData(*PakFilePath);
    if (StatData.bIsValid)
    {
        OutTotalSize = StatData.FileSize;
    }

    // 获取文件列表来计算文件数量
    TArray<FString> FileList = GetPakFileContents(PakFilePath);
    OutFileCount = FileList.Num();

    return true;
}

bool UNeoPakManager::ParsePakFileContents(const FString& PakFilePath, TArray<FString>& OutFileList)
{
    OutFileList.Empty();

    // 获取UnrealPak工具路径
    FString UnrealPakPath = FNeoPakFileCreator::GetUnrealPakPath();
    if (UnrealPakPath.IsEmpty() || !FPlatformFileManager::Get().GetPlatformFile().FileExists(*UnrealPakPath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak tool not found"));
        return false;
    }

    // 构建命令行参数来列出PAK文件内容
    FString Arguments = FString::Printf(TEXT("\"%s\" -List"), *PakFilePath);

    UE_LOG(LogNeoPakTools, Log, TEXT("Executing UnrealPak to list contents: %s %s"), *UnrealPakPath, *Arguments);

    // 执行UnrealPak命令
    FString StdOut;
    FString StdErr;
    int32 ReturnCode = 0;

    bool bSuccess = FPlatformProcess::ExecProcess(
        *UnrealPakPath,
        *Arguments,
        &ReturnCode,
        &StdOut,
        &StdErr
    );

    if (!bSuccess || ReturnCode != 0)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak failed to list PAK contents. Return code: %d"), ReturnCode);
        if (!StdErr.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("UnrealPak error output: %s"), *StdErr);
        }
        return false;
    }

    // 解析输出
    if (StdOut.IsEmpty())
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("UnrealPak returned empty output for: %s"), *PakFilePath);
        return true; // 空PAK文件也算成功
    }

    // 按行分割输出
    TArray<FString> Lines;
    StdOut.ParseIntoArrayLines(Lines);

    // 解析每一行，提取文件路径
    for (const FString& Line : Lines)
    {
        FString TrimmedLine = Line.TrimStartAndEnd();

        UE_LOG(LogNeoPakTools, Log, TEXT("Parsing line: %s"), *TrimmedLine);

        // 跳过空行和非文件行
        if (TrimmedLine.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("  Skipping empty line"));
            continue;
        }

        // 跳过非文件信息行
        if (TrimmedLine.Contains(TEXT("ProjectDir:")) ||
            TrimmedLine.Contains(TEXT("Using command line")) ||
            TrimmedLine.Contains(TEXT("Listing")) ||
            TrimmedLine.Contains(TEXT("files (")) ||
            TrimmedLine.Contains(TEXT("executed in")))
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("  Skipping info line: %s"), *TrimmedLine);
            continue;
        }

        // 只处理包含文件信息的行（包含 offset: 的行）
        if (!TrimmedLine.Contains(TEXT("offset:")))
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("  Skipping line (no offset info): %s"), *TrimmedLine);
            continue;
        }

        // 查找文件信息行的模式: LogPakFile: Display: "filename" offset: ...
        // 但根据您的日志，实际格式是: LogPakFile: Display: "filename" offset: ...
        // 让我们寻找引号之间的内容
        int32 FirstQuoteIndex = TrimmedLine.Find(TEXT("\""));
        int32 SecondQuoteIndex = TrimmedLine.Find(TEXT("\""), ESearchCase::IgnoreCase, ESearchDir::FromStart, FirstQuoteIndex + 1);

        if (FirstQuoteIndex != INDEX_NONE && SecondQuoteIndex != INDEX_NONE)
        {
            // 提取引号之间的文件名
            FString FileName = TrimmedLine.Mid(FirstQuoteIndex + 1, SecondQuoteIndex - FirstQuoteIndex - 1);
            if (!FileName.IsEmpty())
            {
                OutFileList.Add(FileName);
                UE_LOG(LogNeoPakTools, Log, TEXT("  Found file: %s"), *FileName);
            }
        }
        else
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("  Could not parse file name from line: %s"), *TrimmedLine);
        }
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Parsed %d files from PAK: %s"), OutFileList.Num(), *PakFilePath);
    return true;
}

void UNeoPakManager::UpdatePakEntryContents(const FString& PakFilePath)
{
    if (DiscoveredPaks.Contains(PakFilePath))
    {
        TArray<FString> FileList = GetPakFileContents(PakFilePath);
        DiscoveredPaks[PakFilePath].ContainedAssets = FileList;
    }
}

TArray<FString> UNeoPakManager::ResolveDependenciesForPackaging(const TArray<FString>& MainAssetPaths)
{
    TSet<FString> AllAssets;
    TSet<FString> ProcessedAssets;

    // 添加主要资产
    for (const FString& AssetPath : MainAssetPaths)
    {
        AllAssets.Add(AssetPath);
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Resolving dependencies for %d main assets"), MainAssetPaths.Num());

    // 递归解析依赖
    TArray<FString> AssetsToProcess = MainAssetPaths;

    while (AssetsToProcess.Num() > 0)
    {
        FString CurrentAsset = AssetsToProcess.Pop();

        // 跳过已处理的资产
        if (ProcessedAssets.Contains(CurrentAsset))
        {
            continue;
        }

        ProcessedAssets.Add(CurrentAsset);

        // 解析当前资产的依赖
        if (DependencyResolver.IsValid())
        {
            TArray<FString> Dependencies = DependencyResolver->ResolveDependencies(CurrentAsset);

            UE_LOG(LogNeoPakTools, Log, TEXT("Asset %s has %d dependencies"), *CurrentAsset, Dependencies.Num());

            for (const FString& Dependency : Dependencies)
            {
                // 过滤掉不需要的依赖
                if (ShouldIncludeDependency(Dependency))
                {
                    if (!AllAssets.Contains(Dependency))
                    {
                        AllAssets.Add(Dependency);
                        AssetsToProcess.Add(Dependency);
                        UE_LOG(LogNeoPakTools, Log, TEXT("  Added dependency: %s"), *Dependency);
                    }
                }
                else
                {
                    UE_LOG(LogNeoPakTools, Log, TEXT("  Excluded dependency: %s"), *Dependency);
                }
            }
        }
    }

    TArray<FString> Result = AllAssets.Array();
    UE_LOG(LogNeoPakTools, Log, TEXT("Total assets after dependency resolution: %d"), Result.Num());

    return Result;
}

bool UNeoPakManager::ShouldIncludeDependency(const FString& DependencyPath)
{
    // 排除引擎资产
    if (DependencyPath.StartsWith(TEXT("/Engine/")))
    {
        return false;
    }

    // 排除插件资产（除非是我们自己的插件）
    if (DependencyPath.StartsWith(TEXT("/Plugins/")))
    {
        // 可以选择包含特定插件的资产
        // return DependencyPath.StartsWith(TEXT("/Plugins/MyPlugin/"));
        return false;
    }

    // 排除脚本包
    if (DependencyPath.StartsWith(TEXT("/Script/")))
    {
        return false;
    }

    // 排除临时包
    if (DependencyPath.StartsWith(TEXT("/Temp/")))
    {
        return false;
    }

    // 排除内存中的包
    if (DependencyPath.StartsWith(TEXT("/Memory/")))
    {
        return false;
    }

    // 包含游戏内容
    if (DependencyPath.StartsWith(TEXT("/Game/")))
    {
        return true;
    }

    // 默认排除其他类型
    UE_LOG(LogNeoPakTools, Log, TEXT("Unknown dependency type, excluding: %s"), *DependencyPath);
    return false;
}

UNeoPakConfigAssetBase* UNeoPakManager::LoadConfigAssetFromPak(const FString& PakFilePath)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Attempting to load config asset from PAK: %s"), *PakFilePath);

    // 首先获取PAK的类型信息
    if (!FNeoDataAssetTypeRegistry::HasDataAssetTypeInfo(PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No type info found for PAK: %s"), *PakFilePath);
        return nullptr;
    }

    FNeoDataAssetTypeInfo TypeInfo = FNeoDataAssetTypeRegistry::GetDataAssetTypeFromPak(PakFilePath);

    if (TypeInfo.ConfigAssetPath.IsEmpty())
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No config asset path in type info for PAK: %s"), *PakFilePath);
        return nullptr;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Config asset path from PAK: %s"), *TypeInfo.ConfigAssetPath);

    // 尝试加载配置资产
    // 注意：这里需要PAK文件已经被挂载到文件系统中
    UNeoPakConfigAssetBase* ConfigAsset = LoadObject<UNeoPakConfigAssetBase>(nullptr, *TypeInfo.ConfigAssetPath);

    if (ConfigAsset)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Successfully loaded config asset: %s (Type: %s)"),
               *TypeInfo.ConfigAssetPath, *ConfigAsset->GetClass()->GetName());
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to load config asset: %s. Make sure the PAK is mounted."),
               *TypeInfo.ConfigAssetPath);
    }

    return ConfigAsset;
}

bool UNeoPakManager::LoadPakFile(const FString& PakFilePath)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Attempting to load PAK file: %s"), *PakFilePath);

    // 检查文件是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("PAK file does not exist: %s"), *PakFilePath);
        return false;
    }

    // 检查是否已经加载
    if (LoadedPaks.Contains(PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("PAK file is already loaded: %s"), *PakFilePath);
        return true;
    }

    // 使用简化的PAK加载方法
    // 注意：这是一个简化的实现，主要用于状态跟踪
    // 在实际的生产环境中，可能需要更复杂的PAK挂载逻辑

    UE_LOG(LogNeoPakTools, Warning, TEXT("PAK loading is currently implemented for status tracking only"));
    UE_LOG(LogNeoPakTools, Warning, TEXT("For full runtime PAK loading, additional implementation may be required"));

    // 模拟成功加载（用于演示和测试）
    bool bMounted = true;

    if (bMounted)
    {
        // 创建PAK条目
        FNeoPakEntry PakEntry;
        PakEntry.PakFilePath = PakFilePath;
        PakEntry.LoadStatus = EPakLoadStatus::Loaded;
        PakEntry.LoadTime = FDateTime::Now();

        // 获取PAK文件内容
        PakEntry.ContainedAssets = GetPakFileContents(PakFilePath);

        // 添加到已加载列表
        LoadedPaks.Add(PakFilePath, PakEntry);

        // 同时更新发现的PAK列表
        if (DiscoveredPaks.Contains(PakFilePath))
        {
            DiscoveredPaks[PakFilePath].LoadStatus = EPakLoadStatus::Loaded;
            DiscoveredPaks[PakFilePath].LoadTime = PakEntry.LoadTime;
        }

        UE_LOG(LogNeoPakTools, Log, TEXT("Successfully loaded PAK file: %s"), *PakFilePath);
        return true;
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to mount PAK file: %s"), *PakFilePath);
        return false;
    }
}

bool UNeoPakManager::UnloadPakFile(const FString& PakFilePath)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Attempting to unload PAK file: %s"), *PakFilePath);

    // 检查是否已加载
    if (!LoadedPaks.Contains(PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("PAK file is not loaded: %s"), *PakFilePath);
        return true; // 已经未加载状态，返回成功
    }

    // 使用简化的PAK卸载方法
    // 注意：这是一个简化的实现，主要用于状态跟踪

    UE_LOG(LogNeoPakTools, Warning, TEXT("PAK unloading is currently implemented for status tracking only"));

    // 模拟成功卸载（用于演示和测试）
    bool bUnmounted = true;

    if (bUnmounted)
    {
        // 从已加载列表中移除
        LoadedPaks.Remove(PakFilePath);

        // 更新发现的PAK列表状态
        if (DiscoveredPaks.Contains(PakFilePath))
        {
            DiscoveredPaks[PakFilePath].LoadStatus = EPakLoadStatus::NotLoaded;
        }

        UE_LOG(LogNeoPakTools, Log, TEXT("Successfully unloaded PAK file: %s"), *PakFilePath);
        return true;
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to unmount PAK file: %s"), *PakFilePath);
        return false;
    }
}

EPakLoadStatus UNeoPakManager::GetPakLoadStatus(const FString& PakFilePath) const
{
    // 首先检查已加载的PAK列表
    if (LoadedPaks.Contains(PakFilePath))
    {
        return LoadedPaks[PakFilePath].LoadStatus;
    }

    // 检查发现的PAK列表
    if (DiscoveredPaks.Contains(PakFilePath))
    {
        return DiscoveredPaks[PakFilePath].LoadStatus;
    }

    // 检查文件是否存在
    if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath))
    {
        return EPakLoadStatus::NotLoaded;
    }

    // 文件不存在
    return EPakLoadStatus::NotLoaded;
}

bool UNeoPakManager::BatchPackageConfigs(const TArray<UNeoPakConfigAssetBase*>& Configs, bool bShowProgress)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Starting batch packaging for %d configurations"), Configs.Num());

    // 开始操作跟踪
    StartOperation(TEXT("Batch Packaging"), Configs.Num());

    int32 SuccessCount = 0;
    int32 FailureCount = 0;
    TArray<FString> FailedConfigs;

    for (int32 i = 0; i < Configs.Num(); ++i)
    {
        // 检查是否需要取消操作
        if (ShouldCancelOperation())
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("Batch packaging cancelled by user at step %d/%d"), i + 1, Configs.Num());
            FinishOperation();
            return false;
        }

        UNeoPakConfigAssetBase* Config = Configs[i];
        if (!Config)
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("Null config at index %d, skipping"), i);
            FailureCount++;
            FailedConfigs.Add(FString::Printf(TEXT("Index %d (Null Config)"), i));
            UpdateProgress(i + 1, TEXT("Skipped null config"));
            continue;
        }

        FString ConfigName = Config->GetName();
        UE_LOG(LogNeoPakTools, Log, TEXT("Processing config %d/%d: %s"), i + 1, Configs.Num(), *ConfigName);

        // 更新进度
        UpdateProgress(i + 1, FString::Printf(TEXT("Packaging %s"), *ConfigName));

        // 执行打包
        bool bSuccess = ExecutePackaging(Config);

        if (bSuccess)
        {
            SuccessCount++;
            UE_LOG(LogNeoPakTools, Log, TEXT("Successfully packaged: %s"), *ConfigName);
        }
        else
        {
            FailureCount++;
            FailedConfigs.Add(ConfigName);
            UE_LOG(LogNeoPakTools, Error, TEXT("Failed to package: %s"), *ConfigName);
        }
    }

    // 完成操作
    FinishOperation();

    // 输出总结
    UE_LOG(LogNeoPakTools, Log, TEXT("Batch packaging completed: %d success, %d failed out of %d total"),
           SuccessCount, FailureCount, Configs.Num());

    if (FailedConfigs.Num() > 0)
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("Failed configurations:"));
        for (const FString& FailedConfig : FailedConfigs)
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("  - %s"), *FailedConfig);
        }
    }

    return FailureCount == 0;
}

TArray<FString> UNeoPakManager::BatchValidateConfigs(const TArray<UNeoPakConfigAssetBase*>& Configs)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Starting batch validation for %d configurations"), Configs.Num());

    TArray<FString> ValidationErrors;

    for (int32 i = 0; i < Configs.Num(); ++i)
    {
        UNeoPakConfigAssetBase* Config = Configs[i];
        if (!Config)
        {
            FString ErrorMsg = FString::Printf(TEXT("Config at index %d is null"), i);
            ValidationErrors.Add(ErrorMsg);
            UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *ErrorMsg);
            continue;
        }

        FString ConfigName = Config->GetName();
        UE_LOG(LogNeoPakTools, Log, TEXT("Validating config %d/%d: %s"), i + 1, Configs.Num(), *ConfigName);

#if WITH_EDITOR
        // 执行配置验证
        if (!Config->ValidateConfiguration())
        {
            FString ErrorMsg = FString::Printf(TEXT("Configuration validation failed for: %s"), *ConfigName);
            ValidationErrors.Add(ErrorMsg);
            UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *ErrorMsg);
        }
        else
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("Configuration validation passed for: %s"), *ConfigName);
        }
#else
        UE_LOG(LogNeoPakTools, Warning, TEXT("Configuration validation is only available in editor builds"));
#endif
    }

    // 输出总结
    int32 ValidCount = Configs.Num() - ValidationErrors.Num();
    UE_LOG(LogNeoPakTools, Log, TEXT("Batch validation completed: %d valid, %d invalid out of %d total"),
           ValidCount, ValidationErrors.Num(), Configs.Num());

    return ValidationErrors;
}

bool UNeoPakManager::CleanOutputDirectory(bool bConfirmDelete)
{
    // 获取输出目录
    const UNeoPakToolsSettings* Settings = GetDefault<UNeoPakToolsSettings>();
    if (!Settings)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to get NeoPakTools settings"));
        return false;
    }

    FString OutputDirectory = Settings->DefaultOutputDirectory.Path;
    if (FPaths::IsRelative(OutputDirectory))
    {
        OutputDirectory = FPaths::ProjectDir() / OutputDirectory;
    }
    OutputDirectory = FPaths::ConvertRelativePathToFull(OutputDirectory);

    UE_LOG(LogNeoPakTools, Log, TEXT("Cleaning output directory: %s"), *OutputDirectory);

    // 检查目录是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*OutputDirectory))
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("Output directory does not exist: %s"), *OutputDirectory);
        return true; // 目录不存在也算清理成功
    }

    // 查找要删除的文件
    TArray<FString> FilesToDelete;
    TArray<FString> FileExtensions = {TEXT("*.pak"), TEXT("*.json")};

    for (const FString& Extension : FileExtensions)
    {
        TArray<FString> FoundFiles;
        IFileManager::Get().FindFilesRecursive(FoundFiles, *OutputDirectory, *Extension, true, false);
        FilesToDelete.Append(FoundFiles);
    }

    if (FilesToDelete.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("No files to clean in output directory"));
        return true;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d files to delete"), FilesToDelete.Num());

    // 如果需要确认，这里可以添加确认逻辑
    if (bConfirmDelete)
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("Cleaning %d files from output directory"), FilesToDelete.Num());
    }

    // 删除文件
    int32 DeletedCount = 0;
    int32 FailedCount = 0;

    for (const FString& FilePath : FilesToDelete)
    {
        if (IFileManager::Get().Delete(*FilePath))
        {
            DeletedCount++;
            UE_LOG(LogNeoPakTools, Log, TEXT("Deleted: %s"), *FilePath);
        }
        else
        {
            FailedCount++;
            UE_LOG(LogNeoPakTools, Error, TEXT("Failed to delete: %s"), *FilePath);
        }
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Clean completed: %d deleted, %d failed"), DeletedCount, FailedCount);
    return FailedCount == 0;
}

TArray<UNeoPakConfigAssetBase*> UNeoPakManager::GetAllConfigAssets()
{
    TArray<UNeoPakConfigAssetBase*> AllConfigs;

#if WITH_EDITOR
    // 使用Asset Registry查找所有NeoPakConfig资产
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // 查找所有UNeoPakConfigAssetBase的子类
    TArray<FAssetData> AssetDataList;
    FARFilter Filter;
    Filter.ClassPaths.Add(UNeoPakConfigAssetBase::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;

    AssetRegistry.GetAssets(Filter, AssetDataList);

    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d NeoPakConfig assets"), AssetDataList.Num());

    // 加载找到的资产
    for (const FAssetData& AssetData : AssetDataList)
    {
        UNeoPakConfigAssetBase* ConfigAsset = Cast<UNeoPakConfigAssetBase>(AssetData.GetAsset());
        if (ConfigAsset)
        {
            AllConfigs.Add(ConfigAsset);
            UE_LOG(LogNeoPakTools, Log, TEXT("Found config: %s (Type: %s)"),
                   *ConfigAsset->GetName(), *ConfigAsset->GetClass()->GetName());
        }
    }
#else
    UE_LOG(LogNeoPakTools, Warning, TEXT("GetAllConfigAssets is only available in editor builds"));
#endif

    UE_LOG(LogNeoPakTools, Log, TEXT("Loaded %d config assets"), AllConfigs.Num());
    return AllConfigs;
}

TArray<FString> UNeoPakManager::GetMissingDependencies(const FString& PakFilePath)
{
    TArray<FString> MissingDependencies;

    UE_LOG(LogNeoPakTools, Log, TEXT("Checking missing dependencies for PAK: %s"), *PakFilePath);

    // 首先获取PAK文件中的所有文件
    TArray<FString> PakContents = GetPakFileContents(PakFilePath);
    if (PakContents.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("PAK file is empty or could not be read: %s"), *PakFilePath);
        return MissingDependencies;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("PAK contains %d files, checking dependencies..."), PakContents.Num());

    // 检查每个文件的依赖
    TSet<FString> AllRequiredDependencies;
    TSet<FString> AvailableAssets;

    // 将PAK中的文件转换为资产路径格式
    for (const FString& PakFile : PakContents)
    {
        FString AssetPath = ConvertFilePathToAssetPath(PakFile);
        if (!AssetPath.IsEmpty())
        {
            AvailableAssets.Add(AssetPath);
        }
    }

    // 解析每个资产的依赖
    for (const FString& PakFile : PakContents)
    {
        FString AssetPath = ConvertFilePathToAssetPath(PakFile);
        if (!AssetPath.IsEmpty() && DependencyResolver.IsValid())
        {
            TArray<FString> Dependencies = DependencyResolver->ResolveDependencies(AssetPath);
            for (const FString& Dependency : Dependencies)
            {
                if (ShouldIncludeDependency(Dependency))
                {
                    AllRequiredDependencies.Add(Dependency);
                }
            }
        }
    }

    // 找出缺失的依赖
    for (const FString& RequiredDep : AllRequiredDependencies)
    {
        if (!AvailableAssets.Contains(RequiredDep))
        {
            MissingDependencies.Add(RequiredDep);
            UE_LOG(LogNeoPakTools, Warning, TEXT("Missing dependency: %s"), *RequiredDep);
        }
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d missing dependencies for PAK: %s"),
           MissingDependencies.Num(), *PakFilePath);

    return MissingDependencies;
}

FString UNeoPakManager::ConvertFilePathToAssetPath(const FString& FilePath)
{
    // 将PAK中的文件路径转换为UE5资产路径
    // 例如：将 "NewMap.NewMap" 转换为 "/Game/Maps/NewMap.NewMap"

    if (FilePath.IsEmpty())
    {
        return TEXT("");
    }

    FString AssetPath = FilePath;

    // 移除文件扩展名
    int32 DotIndex = AssetPath.Find(TEXT("."), ESearchCase::IgnoreCase, ESearchDir::FromEnd);
    if (DotIndex != INDEX_NONE)
    {
        // 检查是否是资产文件扩展名
        FString Extension = AssetPath.RightChop(DotIndex);
        if (Extension == TEXT(".uasset") || Extension == TEXT(".umap") || Extension == TEXT(".ubulk"))
        {
            AssetPath = AssetPath.Left(DotIndex);
        }
    }

    // 如果不是以/Game/开头，尝试构建完整路径
    if (!AssetPath.StartsWith(TEXT("/Game/")))
    {
        // 假设文件在/Game/目录下
        AssetPath = TEXT("/Game/") + AssetPath;
    }

    return AssetPath;
}

TArray<FString> UNeoPakManager::FindAvailableAssets(const TArray<FString>& MissingDependencies)
{
    TArray<FString> AvailableAssets;

#if WITH_EDITOR
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    for (const FString& MissingDep : MissingDependencies)
    {
        // 尝试在Asset Registry中查找这个资产
        FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(FSoftObjectPath(MissingDep));

        if (AssetData.IsValid())
        {
            AvailableAssets.Add(MissingDep);
            UE_LOG(LogNeoPakTools, Log, TEXT("Found available asset for missing dependency: %s"), *MissingDep);
        }
        else
        {
            // 尝试模糊匹配
            FString AssetName = FPaths::GetBaseFilename(MissingDep);
            TArray<FAssetData> FoundAssets;
            // 使用GetAssets方法替代GetAssetsByName
            FARFilter Filter;
            Filter.PackageNames.Add(FName(*AssetName));
            AssetRegistry.GetAssets(Filter, FoundAssets);

            for (const FAssetData& FoundAsset : FoundAssets)
            {
                FString FoundAssetPath = FoundAsset.GetSoftObjectPath().ToString();
                if (ShouldIncludeDependency(FoundAssetPath))
                {
                    AvailableAssets.Add(FoundAssetPath);
                    UE_LOG(LogNeoPakTools, Log, TEXT("Found similar asset for %s: %s"), *MissingDep, *FoundAssetPath);
                    break; // 只取第一个匹配的
                }
            }
        }
    }
#endif

    return AvailableAssets;
}

bool UNeoPakManager::CanAutoFixDependency(const FString& DependencyPath)
{
    // 检查是否可以自动修复这个依赖

    // 不修复引擎资产
    if (DependencyPath.StartsWith(TEXT("/Engine/")))
    {
        return false;
    }

    // 不修复插件资产
    if (DependencyPath.StartsWith(TEXT("/Plugins/")))
    {
        return false;
    }

    // 不修复脚本包
    if (DependencyPath.StartsWith(TEXT("/Script/")))
    {
        return false;
    }

    // 只修复游戏内容
    if (DependencyPath.StartsWith(TEXT("/Game/")))
    {
        return true;
    }

    return false;
}

bool UNeoPakManager::AutoFixDependencies(UNeoPakConfigAssetBase* ConfigAsset, const FNeoDependencyCheckResult& DependencyResult)
{
    if (!ConfigAsset)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("ConfigAsset is null"));
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Starting auto-fix dependencies for config: %s"), *ConfigAsset->ConfigName);

    // 分析依赖问题
    TArray<FString> MissingDependencies;
    TArray<FString> ConflictingDependencies;
    TArray<FString> CircularDependencies;

    // 从依赖检查结果中提取问题
    for (const FString& Issue : DependencyResult.Issues)
    {
        if (Issue.Contains(TEXT("Missing")))
        {
            // 提取缺失的依赖路径
            FString DependencyPath = ExtractDependencyPathFromIssue(Issue);
            if (!DependencyPath.IsEmpty())
            {
                MissingDependencies.Add(DependencyPath);
            }
        }
        else if (Issue.Contains(TEXT("Conflict")))
        {
            FString DependencyPath = ExtractDependencyPathFromIssue(Issue);
            if (!DependencyPath.IsEmpty())
            {
                ConflictingDependencies.Add(DependencyPath);
            }
        }
        else if (Issue.Contains(TEXT("Circular")))
        {
            FString DependencyPath = ExtractDependencyPathFromIssue(Issue);
            if (!DependencyPath.IsEmpty())
            {
                CircularDependencies.Add(DependencyPath);
            }
        }
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d missing, %d conflicting, %d circular dependencies"),
           MissingDependencies.Num(), ConflictingDependencies.Num(), CircularDependencies.Num());

    bool bAllFixed = true;

    // 修复缺失的依赖
    if (MissingDependencies.Num() > 0)
    {
        bool bMissingFixed = FixMissingDependencies(ConfigAsset, MissingDependencies);
        if (!bMissingFixed)
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("Failed to fix some missing dependencies"));
            bAllFixed = false;
        }
    }

    // 修复冲突的依赖
    if (ConflictingDependencies.Num() > 0)
    {
        bool bConflictsFixed = FixConflictingDependencies(ConfigAsset, ConflictingDependencies);
        if (!bConflictsFixed)
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("Failed to fix some conflicting dependencies"));
            bAllFixed = false;
        }
    }

    // 修复循环依赖
    if (CircularDependencies.Num() > 0)
    {
        bool bCircularFixed = FixCircularDependencies(ConfigAsset, CircularDependencies);
        if (!bCircularFixed)
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("Failed to fix some circular dependencies"));
            bAllFixed = false;
        }
    }

    if (bAllFixed)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Successfully auto-fixed all dependencies for config: %s"), *ConfigAsset->ConfigName);
    }
    else
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("Auto-fix completed with some unresolved issues for config: %s"), *ConfigAsset->ConfigName);
    }

    return bAllFixed;
}

FString UNeoPakManager::ExtractDependencyPathFromIssue(const FString& Issue)
{
    // 从问题描述中提取依赖路径
    // 例如："Missing dependency: /Game/Characters/Hero" -> "/Game/Characters/Hero"

    FString DependencyPath;

    // 查找冒号后的路径
    int32 ColonIndex = Issue.Find(TEXT(":"));
    if (ColonIndex != INDEX_NONE)
    {
        DependencyPath = Issue.RightChop(ColonIndex + 1).TrimStartAndEnd();
    }

    return DependencyPath;
}

bool UNeoPakManager::FixMissingDependencies(UNeoPakConfigAssetBase* ConfigAsset, const TArray<FString>& MissingDependencies)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Fixing %d missing dependencies for config: %s"),
           MissingDependencies.Num(), *ConfigAsset->ConfigName);

    TArray<FString> FixedDependencies;
    TArray<FString> UnfixableDependencies;

    for (const FString& MissingDep : MissingDependencies)
    {
        if (!CanAutoFixDependency(MissingDep))
        {
            UE_LOG(LogNeoPakTools, Warning, TEXT("Cannot auto-fix dependency (not a game asset): %s"), *MissingDep);
            UnfixableDependencies.Add(MissingDep);
            continue;
        }

        // 尝试查找可用的替代资产
        TArray<FString> Alternatives = FindAvailableAssets({MissingDep});

        if (Alternatives.Num() > 0)
        {
            // 找到了替代资产，添加到配置中
            FString Alternative = Alternatives[0];

            // 这里需要根据配置类型添加资产
            bool bAdded = AddAssetToConfig(ConfigAsset, Alternative);

            if (bAdded)
            {
                FixedDependencies.Add(MissingDep);
                UE_LOG(LogNeoPakTools, Log, TEXT("Fixed missing dependency %s with alternative: %s"),
                       *MissingDep, *Alternative);
            }
            else
            {
                UnfixableDependencies.Add(MissingDep);
                UE_LOG(LogNeoPakTools, Warning, TEXT("Found alternative for %s but failed to add to config: %s"),
                       *MissingDep, *Alternative);
            }
        }
        else
        {
            UnfixableDependencies.Add(MissingDep);
            UE_LOG(LogNeoPakTools, Warning, TEXT("No alternative found for missing dependency: %s"), *MissingDep);
        }
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Missing dependencies fix result: %d fixed, %d unfixable"),
           FixedDependencies.Num(), UnfixableDependencies.Num());

    return UnfixableDependencies.Num() == 0;
}

bool UNeoPakManager::FixConflictingDependencies(UNeoPakConfigAssetBase* ConfigAsset, const TArray<FString>& ConflictingDependencies)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Fixing %d conflicting dependencies for config: %s"),
           ConflictingDependencies.Num(), *ConfigAsset->ConfigName);

    // 冲突依赖的修复策略：
    // 1. 移除重复的依赖
    // 2. 选择最新版本的资产
    // 3. 解决版本冲突

    TArray<FString> ResolvedConflicts;

    for (const FString& ConflictingDep : ConflictingDependencies)
    {
        // 简单的冲突解决：记录但不做实际修改
        // 在实际项目中，这里可以实现更复杂的冲突解决逻辑
        UE_LOG(LogNeoPakTools, Warning, TEXT("Conflicting dependency detected but not auto-fixed: %s"), *ConflictingDep);
        ResolvedConflicts.Add(ConflictingDep);
    }

    // 目前简单返回true，表示"已处理"（即使只是记录）
    return true;
}

bool UNeoPakManager::FixCircularDependencies(UNeoPakConfigAssetBase* ConfigAsset, const TArray<FString>& CircularDependencies)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Fixing %d circular dependencies for config: %s"),
           CircularDependencies.Num(), *ConfigAsset->ConfigName);

    // 循环依赖的修复策略：
    // 1. 识别循环链
    // 2. 移除非关键的依赖
    // 3. 重新组织依赖结构

    TArray<FString> ResolvedCircular;

    for (const FString& CircularDep : CircularDependencies)
    {
        // 简单的循环依赖处理：记录警告
        // 在实际项目中，这里可以实现循环依赖的检测和解决
        UE_LOG(LogNeoPakTools, Warning, TEXT("Circular dependency detected but not auto-fixed: %s"), *CircularDep);
        ResolvedCircular.Add(CircularDep);
    }

    // 目前简单返回true，表示"已处理"
    return true;
}

bool UNeoPakManager::AddAssetToConfig(UNeoPakConfigAssetBase* ConfigAsset, const FString& AssetPath)
{
    if (!ConfigAsset)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("ConfigAsset is null"));
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Attempting to add asset to config %s: %s"),
           *ConfigAsset->ConfigName, *AssetPath);

    // 根据配置类型和资产类型，决定如何添加资产
    // 这是一个简化的实现，实际项目中可能需要更复杂的逻辑

#if WITH_EDITOR
    // 创建软对象路径
    FSoftObjectPath SoftObjectPath(AssetPath);

    // 尝试加载资产以验证其有效性
    UObject* Asset = SoftObjectPath.TryLoad();
    if (!Asset)
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("Failed to load asset: %s"), *AssetPath);
        return false;
    }

    // 根据配置类型添加资产
    // 注意：这里需要根据实际的配置类型实现具体的添加逻辑
    // 目前只是一个示例实现

    UE_LOG(LogNeoPakTools, Log, TEXT("Asset loaded successfully, but automatic addition to config is not fully implemented"));
    UE_LOG(LogNeoPakTools, Log, TEXT("Asset type: %s, Config type: %s"),
           *Asset->GetClass()->GetName(), *ConfigAsset->GetClass()->GetName());

    // 在实际实现中，这里应该调用配置特定的方法来添加资产
    // 例如：
    // if (UNeoMapPakConfig* MapConfig = Cast<UNeoMapPakConfig>(ConfigAsset))
    // {
    //     if (UWorld* World = Cast<UWorld>(Asset))
    //     {
    //         MapConfig->Map = SoftObjectPath;
    //         return true;
    //     }
    // }

    return true; // 暂时返回true表示"已处理"
#else
    UE_LOG(LogNeoPakTools, Warning, TEXT("AddAssetToConfig is only available in editor builds"));
    return false;
#endif
}

bool UNeoPakManager::CreatePakFileWithAdvancedOptions(const TArray<FString>& AssetPaths, const FString& OutputPath, const FNeoPakCompressionSettings& CompressionSettings, const FNeoPakEncryptionSettings& EncryptionSettings)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Creating PAK file with advanced options: %s"), *OutputPath);
    UE_LOG(LogNeoPakTools, Log, TEXT("Compression enabled: %s, Encryption enabled: %s"),
           CompressionSettings.bEnableCompression ? TEXT("Yes") : TEXT("No"),
           EncryptionSettings.bEnableEncryption ? TEXT("Yes") : TEXT("No"));

    if (AssetPaths.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No assets provided for PAK creation"));
        return false;
    }

    // 创建临时响应文件
    FString ResponseFilePath = FPaths::CreateTempFilename(*FPaths::ProjectIntermediateDir(), TEXT("NeoPakResponse"), TEXT(".txt"));

    if (!CreateAdvancedResponseFile(AssetPaths, ResponseFilePath, CompressionSettings))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to create advanced response file"));
        return false;
    }

    // 构建高级UnrealPak命令
    FString UnrealPakCommand = BuildAdvancedUnrealPakCommand(OutputPath, ResponseFilePath, CompressionSettings, EncryptionSettings);

    UE_LOG(LogNeoPakTools, Log, TEXT("Executing advanced UnrealPak command: %s"), *UnrealPakCommand);

    // 执行UnrealPak命令
    int32 ReturnCode;
    FString StdOut;
    FString StdErr;

    bool bSuccess = FPlatformProcess::ExecProcess(
        *GetUnrealPakPath(),
        *UnrealPakCommand,
        &ReturnCode,
        &StdOut,
        &StdErr
    );

    // 清理临时文件
    IFileManager::Get().Delete(*ResponseFilePath);

    if (bSuccess && ReturnCode == 0)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Advanced PAK creation successful: %s"), *OutputPath);

        // 如果启用了签名生成
        const UNeoPakToolsSettings* Settings = GetDefault<UNeoPakToolsSettings>();
        if (Settings && Settings->bGeneratePakSignature)
        {
            GeneratePakSignature(OutputPath);
        }

        // 如果启用了完整性验证
        if (Settings && Settings->bVerifyPakIntegrity)
        {
            VerifyPakFileIntegrity(OutputPath);
        }

        return true;
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Advanced PAK creation failed. Return code: %d"), ReturnCode);
        UE_LOG(LogNeoPakTools, Error, TEXT("StdOut: %s"), *StdOut);
        UE_LOG(LogNeoPakTools, Error, TEXT("StdErr: %s"), *StdErr);
        return false;
    }
}

bool UNeoPakManager::CreateIncrementalPakFile(const TArray<FString>& AssetPaths, const FString& OutputPath, const FString& BasePakPath)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Creating incremental PAK file: %s (Base: %s)"), *OutputPath, *BasePakPath);

    // 检查基础PAK文件是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*BasePakPath))
    {
        UE_LOG(LogNeoPakTools, Warning, TEXT("Base PAK file does not exist, creating full PAK instead: %s"), *BasePakPath);

        // 使用默认设置创建完整PAK
        const UNeoPakToolsSettings* Settings = GetDefault<UNeoPakToolsSettings>();
        if (Settings)
        {
            return CreatePakFileWithAdvancedOptions(AssetPaths, OutputPath, Settings->DefaultCompressionSettings, Settings->DefaultEncryptionSettings);
        }
        else
        {
            return CreatePakFile(AssetPaths, OutputPath);
        }
    }

    // 检查哪些资产发生了变化
    TArray<FString> ChangedAssets;
    if (!CheckIncrementalChanges(AssetPaths, BasePakPath, ChangedAssets))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to check incremental changes"));
        return false;
    }

    if (ChangedAssets.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("No changes detected, incremental PAK not needed"));
        return true;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Found %d changed assets for incremental PAK"), ChangedAssets.Num());

    // 创建增量PAK文件
    const UNeoPakToolsSettings* Settings = GetDefault<UNeoPakToolsSettings>();
    if (Settings)
    {
        return CreatePakFileWithAdvancedOptions(ChangedAssets, OutputPath, Settings->DefaultCompressionSettings, Settings->DefaultEncryptionSettings);
    }
    else
    {
        return CreatePakFile(ChangedAssets, OutputPath);
    }
}

bool UNeoPakManager::VerifyPakFileIntegrity(const FString& PakFilePath)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Verifying PAK file integrity: %s"), *PakFilePath);

    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("PAK file does not exist: %s"), *PakFilePath);
        return false;
    }

    // 使用UnrealPak的测试功能验证文件完整性
    FString UnrealPakPath = GetUnrealPakPath();
    FString Arguments = FString::Printf(TEXT("\"%s\" -Test"), *PakFilePath);

    UE_LOG(LogNeoPakTools, Log, TEXT("Executing integrity check: %s %s"), *UnrealPakPath, *Arguments);

    int32 ReturnCode;
    FString StdOut;
    FString StdErr;

    bool bSuccess = FPlatformProcess::ExecProcess(
        *UnrealPakPath,
        *Arguments,
        &ReturnCode,
        &StdOut,
        &StdErr
    );

    if (bSuccess && ReturnCode == 0)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("PAK file integrity verification passed: %s"), *PakFilePath);
        return true;
    }
    else
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("PAK file integrity verification failed: %s"), *PakFilePath);
        UE_LOG(LogNeoPakTools, Error, TEXT("Return code: %d"), ReturnCode);
        UE_LOG(LogNeoPakTools, Error, TEXT("StdOut: %s"), *StdOut);
        UE_LOG(LogNeoPakTools, Error, TEXT("StdErr: %s"), *StdErr);
        return false;
    }
}

FString UNeoPakManager::BuildAdvancedUnrealPakCommand(const FString& PakFilePath, const FString& ResponseFilePath, const FNeoPakCompressionSettings& CompressionSettings, const FNeoPakEncryptionSettings& EncryptionSettings)
{
    TArray<FString> Arguments;

    // 基本参数
    Arguments.Add(FString::Printf(TEXT("\"%s\""), *PakFilePath));
    Arguments.Add(FString::Printf(TEXT("-create=\"%s\""), *ResponseFilePath));

    // 压缩设置
    if (CompressionSettings.bEnableCompression)
    {
        // 压缩方法
        switch (CompressionSettings.CompressionMethod)
        {
        case EPakCompressionMethod::None:
            Arguments.Add(TEXT("-compress=None"));
            break;
        case EPakCompressionMethod::Zlib:
            Arguments.Add(TEXT("-compress=Zlib"));
            break;
        case EPakCompressionMethod::Gzip:
            Arguments.Add(TEXT("-compress=Gzip"));
            break;
        case EPakCompressionMethod::LZ4:
            Arguments.Add(TEXT("-compress=LZ4"));
            break;
        }

        // 压缩级别
        if (CompressionSettings.CompressionLevel != 6) // 6是默认值
        {
            Arguments.Add(FString::Printf(TEXT("-compressionlevel=%d"), CompressionSettings.CompressionLevel));
        }

        // 压缩块大小
        if (CompressionSettings.CompressionBlockSize != 256) // 256KB是默认值
        {
            Arguments.Add(FString::Printf(TEXT("-blocksize=%d"), CompressionSettings.CompressionBlockSize * 1024));
        }
    }
    else
    {
        Arguments.Add(TEXT("-compress=None"));
    }

    // 加密设置
    if (EncryptionSettings.bEnableEncryption && !EncryptionSettings.EncryptionKey.IsEmpty())
    {
        Arguments.Add(FString::Printf(TEXT("-cryptokeys=\"%s\""), *EncryptionSettings.EncryptionKey));

        if (EncryptionSettings.bEncryptPakIndex)
        {
            Arguments.Add(TEXT("-encryptindex"));
        }

        if (EncryptionSettings.bEncryptAllFiles)
        {
            Arguments.Add(TEXT("-encrypt"));
        }
    }

    // 自定义参数
    const UNeoPakToolsSettings* Settings = GetDefault<UNeoPakToolsSettings>();
    if (Settings && !Settings->CustomUnrealPakArguments.IsEmpty())
    {
        Arguments.Add(Settings->CustomUnrealPakArguments);
    }

    return FString::Join(Arguments, TEXT(" "));
}

bool UNeoPakManager::CreateAdvancedResponseFile(const TArray<FString>& AssetPaths, const FString& ResponseFilePath, const FNeoPakCompressionSettings& CompressionSettings)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Creating advanced response file: %s"), *ResponseFilePath);

    TArray<FString> FilePaths = ConvertAssetPathsToFilePaths(AssetPaths);
    if (FilePaths.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No valid file paths found for response file"));
        return false;
    }

    TArray<FString> ResponseLines;
    for (int32 i = 0; i < AssetPaths.Num() && i < FilePaths.Num(); ++i)
    {
        const FString& AssetPath = AssetPaths[i];
        const FString& FilePath = FilePaths[i];

        if (FilePath.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Empty file path for asset: %s"), *AssetPath);
            continue;
        }

        // 检查源文件是否存在
        if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("Source file does not exist: %s"), *FilePath);
            continue;
        }

        // 构建响应文件行，包含压缩设置
        // 将资产路径转换为PAK内的路径格式
        FString PakInternalPath = AssetPath;
        if (PakInternalPath.StartsWith(TEXT("/Game/")))
        {
            PakInternalPath = PakInternalPath.RightChop(6); // 移除"/Game/"前缀
        }

        FString ResponseLine = FString::Printf(TEXT("\"%s\" \"%s\""), *FilePath, *PakInternalPath);

        // 添加文件特定的压缩设置（如果需要）
        if (CompressionSettings.bEnableCompression)
        {
            // 可以在这里添加文件特定的压缩选项
            // 例如：ResponseLine += TEXT(" -compress");
        }

        ResponseLines.Add(ResponseLine);
        UE_LOG(LogNeoPakTools, Log, TEXT("Added to advanced response file: %s"), *ResponseLine);
    }

    if (ResponseLines.Num() == 0)
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("No valid entries for response file"));
        return false;
    }

    // 写入响应文件
    FString ResponseContent = FString::Join(ResponseLines, TEXT("\n"));
    if (!FFileHelper::SaveStringToFile(ResponseContent, *ResponseFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to save advanced response file: %s"), *ResponseFilePath);
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Created advanced response file: %s with %d entries"), *ResponseFilePath, ResponseLines.Num());
    return true;
}

bool UNeoPakManager::GeneratePakSignature(const FString& PakFilePath)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Generating PAK signature for: %s"), *PakFilePath);

    // 这里可以实现PAK文件签名生成
    // 目前只是一个占位符实现
    FString SignatureFilePath = PakFilePath + TEXT(".sig");

    // 生成简单的校验和作为签名
    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *PakFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to read PAK file for signature generation: %s"), *PakFilePath);
        return false;
    }

    // 计算简单的哈希值
    uint32 Hash = FCrc::MemCrc32(FileData.GetData(), FileData.Num());
    FString SignatureContent = FString::Printf(TEXT("PAK_SIGNATURE=%08X\nFILE_SIZE=%d\nCREATED=%s"),
                                               Hash, FileData.Num(), *FDateTime::Now().ToString());

    if (!FFileHelper::SaveStringToFile(SignatureContent, *SignatureFilePath))
    {
        UE_LOG(LogNeoPakTools, Error, TEXT("Failed to save PAK signature file: %s"), *SignatureFilePath);
        return false;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Generated PAK signature: %s"), *SignatureFilePath);
    return true;
}

bool UNeoPakManager::CheckIncrementalChanges(const TArray<FString>& AssetPaths, const FString& BasePakPath, TArray<FString>& OutChangedAssets)
{
    UE_LOG(LogNeoPakTools, Log, TEXT("Checking incremental changes against base PAK: %s"), *BasePakPath);

    OutChangedAssets.Empty();

    // 获取基础PAK的内容和时间戳
    TArray<FString> BasePakContents = GetPakFileContents(BasePakPath);
    FDateTime BasePakTime = IFileManager::Get().GetTimeStamp(*BasePakPath);

    UE_LOG(LogNeoPakTools, Log, TEXT("Base PAK contains %d files, created: %s"),
           BasePakContents.Num(), *BasePakTime.ToString());

    // 将资产路径转换为文件路径
    TArray<FString> FilePaths = ConvertAssetPathsToFilePaths(AssetPaths);

    for (int32 i = 0; i < AssetPaths.Num() && i < FilePaths.Num(); ++i)
    {
        const FString& AssetPath = AssetPaths[i];
        const FString& FilePath = FilePaths[i];

        if (FilePath.IsEmpty() || !FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
        {
            continue;
        }

        // 检查文件是否在基础PAK中
        FString AssetFileName = FPaths::GetBaseFilename(AssetPath);
        bool bFoundInBasePak = false;

        for (const FString& BasePakFile : BasePakContents)
        {
            if (BasePakFile.Contains(AssetFileName))
            {
                bFoundInBasePak = true;
                break;
            }
        }

        // 如果文件不在基础PAK中，或者文件比基础PAK新，则认为是变化的
        FDateTime FileTime = IFileManager::Get().GetTimeStamp(*FilePath);

        if (!bFoundInBasePak || FileTime > BasePakTime)
        {
            OutChangedAssets.Add(AssetPath);
            UE_LOG(LogNeoPakTools, Log, TEXT("Changed asset detected: %s (File time: %s)"),
                   *AssetPath, *FileTime.ToString());
        }
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Incremental check completed: %d changed assets out of %d total"),
           OutChangedAssets.Num(), AssetPaths.Num());

    return true;
}

void UNeoPakManager::StartOperation(const FString& OperationName, int32 TotalSteps)
{
    FScopeLock Lock(&OperationLock);

    bOperationInProgress.store(true);
    bCancelRequested.store(false);
    CurrentOperationStep = 0;
    TotalOperationSteps = TotalSteps;
    CurrentOperationName = OperationName;

    UE_LOG(LogNeoPakTools, Log, TEXT("Starting operation: %s (%d steps)"), *OperationName, TotalSteps);

    // 触发进度更新事件
    if (OnProgressUpdated.IsBound())
    {
        OnProgressUpdated.Broadcast(0, TotalSteps, OperationName);
    }
}

void UNeoPakManager::UpdateProgress(int32 CurrentStep, const FString& CurrentStepName)
{
    FScopeLock Lock(&OperationLock);

    CurrentOperationStep = CurrentStep;

    FString StepDescription = CurrentStepName.IsEmpty() ?
        FString::Printf(TEXT("Step %d/%d"), CurrentStep, TotalOperationSteps) :
        FString::Printf(TEXT("Step %d/%d: %s"), CurrentStep, TotalOperationSteps, *CurrentStepName);

    UE_LOG(LogNeoPakTools, Log, TEXT("Progress update: %s - %s"), *CurrentOperationName, *StepDescription);

    // 触发进度更新事件
    if (OnProgressUpdated.IsBound())
    {
        OnProgressUpdated.Broadcast(CurrentStep, TotalOperationSteps, StepDescription);
    }
}

void UNeoPakManager::FinishOperation()
{
    FScopeLock Lock(&OperationLock);

    bOperationInProgress.store(false);
    bCancelRequested.store(false);

    UE_LOG(LogNeoPakTools, Log, TEXT("Operation completed: %s"), *CurrentOperationName);

    // 触发完成事件
    if (OnProgressUpdated.IsBound())
    {
        OnProgressUpdated.Broadcast(TotalOperationSteps, TotalOperationSteps, TEXT("Operation completed"));
    }

    CurrentOperationName = TEXT("");
}

bool UNeoPakManager::ShouldCancelOperation() const
{
    return bCancelRequested.load();
}

void UNeoPakManager::CancelCurrentOperation()
{
    UE_LOG(LogNeoPakTools, Warning, TEXT("Cancelling current operation: %s"), *CurrentOperationName);

    bCancelRequested.store(true);

    if (OnOperationCancelled.IsBound())
    {
        OnOperationCancelled.Broadcast();
    }
}

bool UNeoPakManager::IsOperationInProgress() const
{
    return bOperationInProgress.load();
}

FString UNeoPakManager::GetUnrealPakPath() const
{
    // 获取UnrealPak工具的路径
    FString EngineBinariesPath = FPaths::EngineDir() / TEXT("Binaries");

#if PLATFORM_WINDOWS
    FString UnrealPakPath = EngineBinariesPath / TEXT("Win64") / TEXT("UnrealPak.exe");
#elif PLATFORM_MAC
    FString UnrealPakPath = EngineBinariesPath / TEXT("Mac") / TEXT("UnrealPak");
#elif PLATFORM_LINUX
    FString UnrealPakPath = EngineBinariesPath / TEXT("Linux") / TEXT("UnrealPak");
#else
    FString UnrealPakPath = EngineBinariesPath / TEXT("UnrealPak");
#endif

    return UnrealPakPath;
}

TArray<FString> UNeoPakManager::ConvertAssetPathsToFilePaths(const TArray<FString>& AssetPaths) const
{
    TArray<FString> FilePaths;

    for (const FString& AssetPath : AssetPaths)
    {
        UE_LOG(LogNeoPakTools, Log, TEXT("Converting asset path: %s"), *AssetPath);

        FString FilePath;
        bool bConversionSuccessful = false;

        // 首先尝试使用FNeoPakFileCreator的替代转换方法（更可靠）
        FString AlternativeFilePath = ConvertAssetPathAlternativeInternal(AssetPath);
        if (!AlternativeFilePath.IsEmpty())
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("  Alternative conversion successful: %s"), *AlternativeFilePath);

            // 验证文件是否真的存在
            if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*AlternativeFilePath))
            {
                UE_LOG(LogNeoPakTools, Log, TEXT("  File exists, using alternative path"));
                FilePaths.Add(AlternativeFilePath);
                bConversionSuccessful = true;
            }
            else
            {
                UE_LOG(LogNeoPakTools, Warning, TEXT("  Alternative path does not exist: %s"), *AlternativeFilePath);
            }
        }

        if (!bConversionSuccessful)
        {
            // 回退到标准转换
            if (FPackageName::TryConvertLongPackageNameToFilename(AssetPath, FilePath))
            {
                // 转换为绝对路径
                if (FPaths::IsRelative(FilePath))
                {
                    FilePath = FPaths::ConvertRelativePathToFull(FilePath);
                }

                UE_LOG(LogNeoPakTools, Log, TEXT("  Standard conversion result: %s"), *FilePath);

                // 验证标准转换的文件是否存在
                if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
                {
                    UE_LOG(LogNeoPakTools, Log, TEXT("  Standard conversion file exists"));
                    FilePaths.Add(FilePath);
                    bConversionSuccessful = true;
                }
                else
                {
                    UE_LOG(LogNeoPakTools, Warning, TEXT("  Standard conversion file does not exist: %s"), *FilePath);
                }
            }
            else
            {
                UE_LOG(LogNeoPakTools, Warning, TEXT("  Standard conversion failed for: %s"), *AssetPath);
            }
        }

        if (!bConversionSuccessful)
        {
            UE_LOG(LogNeoPakTools, Error, TEXT("  Failed to convert asset path to file path: %s"), *AssetPath);
            FilePaths.Add(TEXT(""));
        }
    }

    return FilePaths;
}

FNeoDependencyCheckResult UNeoPakManager::CheckDependencies(UNeoPakConfigAssetBase* ConfigAsset)
{
    FNeoDependencyCheckResult Result;
    Result.bCheckPassed = true;

    if (!ConfigAsset)
    {
        Result.bCheckPassed = false;
        Result.ErrorMessages.Add(TEXT("ConfigAsset is null"));
        Result.Issues.Add(TEXT("ConfigAsset is null"));
        return Result;
    }

    UE_LOG(LogNeoPakTools, Log, TEXT("Checking dependencies for config: %s"), *ConfigAsset->GetName());

    // 这里可以实现具体的依赖检查逻辑
    // 目前返回成功状态

    return Result;
}
